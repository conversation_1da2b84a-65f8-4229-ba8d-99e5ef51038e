name: Flutter Test Suite

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'lib/**'
      - 'test/**'
      - 'pubspec.yaml'
      - 'analysis_options.yaml'
      - '.github/workflows/test.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'lib/**'
      - 'test/**'
      - 'pubspec.yaml'
      - 'analysis_options.yaml'
      - '.github/workflows/test.yml'

permissions:
  contents: read
  pull-requests: write
  checks: write

env:
  FLUTTER_VERSION: '3.29.2'
  PUB_CACHE: ~/.pub-cache

jobs:
  # Code quality checks - run first and fast
  code-quality:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Cache pub dependencies
      uses: actions/cache@v4
      with:
        path: |
          ${{ env.PUB_CACHE }}
          ~/.pub-cache
        key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pub-

    - name: Get dependencies
      run: flutter pub get

    - name: Flutter doctor (condensed)
      run: flutter doctor

    - name: Analyze project source
      run: flutter analyze --fatal-infos

    - name: Check formatting
      run: dart format --set-exit-if-changed .

  # Unit tests with parallel execution
  test:
    needs: code-quality
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    strategy:
      matrix:
        shard: [1, 2]
      fail-fast: false

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Restore pub dependencies
      uses: actions/cache@v4
      with:
        path: |
          ${{ env.PUB_CACHE }}
          ~/.pub-cache
        key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pub-

    - name: Get dependencies
      run: flutter pub get

    - name: Run tests with coverage (shard ${{ matrix.shard }}/2)
      run: |
        flutter test \
          --coverage \
          --reporter expanded \
          --shard-index ${{ matrix.shard }} \
          --total-shards 2 \
          --timeout 300s

    - name: Upload coverage artifact
      uses: actions/upload-artifact@v4
      with:
        name: coverage-shard-${{ matrix.shard }}
        path: coverage/
        retention-days: 1

  # Merge coverage and upload
  coverage-report:
    needs: test
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Download coverage artifacts
      uses: actions/download-artifact@v4
      with:
        pattern: coverage-shard-*
        path: coverage-artifacts/

    - name: Install lcov
      run: sudo apt-get update && sudo apt-get install -y lcov

    - name: Merge and generate coverage report
      run: |
        mkdir -p merged-coverage
        # Use the first existing lcov file as initial seed instead of empty file
        first_file=""
        for file in coverage-artifacts/*/lcov.info; do
          if [ -f "$file" ]; then
            if [ -z "$first_file" ]; then
              first_file="$file"
              cp "$file" merged-coverage/lcov.info
              echo "Using $file as initial seed"
            else
              lcov -a merged-coverage/lcov.info -a "$file" -o merged-coverage/lcov.info
              echo "Merged $file"
            fi
          fi
        done
        # Check if we found any lcov files
        if [ -z "$first_file" ]; then
          echo "No lcov files found to merge"
          echo "" > merged-coverage/lcov.info
        fi
        lcov --summary merged-coverage/lcov.info
        lcov --list merged-coverage/lcov.info

    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: merged-coverage/lcov.info
        retention-days: 1

  integration-test:
    needs: code-quality
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Restore pub dependencies
      uses: actions/cache@v4
      with:
        path: |
          ${{ env.PUB_CACHE }}
          ~/.pub-cache
        key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pub-

    - name: Get dependencies
      run: flutter pub get

    - name: Enable desktop support
      run: flutter config --enable-linux-desktop

    - name: Install Linux dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          xvfb \
          ninja-build \
          libgtk-3-dev \
          libblkid-dev \
          liblzma-dev

    - name: Run integration tests on Linux
      run: |
        export DISPLAY=:99
        sudo Xvfb -ac :99 -screen 0 1280x1024x24 > /dev/null 2>&1 &
        sleep 3
        flutter test integration_test/ -d linux --timeout 300s

  build-test:
    needs: code-quality
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      matrix:
        build-target: [apk-debug, web, linux-debug]
      fail-fast: false
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Java (for Android builds)
      if: matrix.build-target == 'apk-debug'
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: Restore pub dependencies
      uses: actions/cache@v4
      with:
        path: |
          ${{ env.PUB_CACHE }}
          ~/.pub-cache
        key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pub-

    - name: Get dependencies
      run: flutter pub get

    - name: Build APK (debug)
      if: matrix.build-target == 'apk-debug'
      run: flutter build apk --debug

    - name: Build web app
      if: matrix.build-target == 'web'
      run: flutter build web

    - name: Build Linux app (debug)
      if: matrix.build-target == 'linux-debug'
      run: |
        flutter config --enable-linux-desktop
        sudo apt-get update -y
        sudo apt-get install -y ninja-build libgtk-3-dev
        flutter build linux --debug

  # Test summary
  test-summary:
    needs: [code-quality, test, coverage-report, integration-test, build-test]
    if: always()
    runs-on: ubuntu-latest
    
    steps:
    - name: Test Results Summary
      run: |
        echo "## Flutter Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- Code Quality: ${{ needs.code-quality.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Unit Tests: ${{ needs.test.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Coverage Report: ${{ needs.coverage-report.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Integration Tests: ${{ needs.integration-test.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Build Tests: ${{ needs.build-test.result }}" >> $GITHUB_STEP_SUMMARY

    - name: Check if critical tests passed
      if: |
        needs.code-quality.result != 'success' ||
        needs.test.result != 'success' ||
        needs.coverage-report.result != 'success'
      run: |
        echo "Critical tests failed!"
        exit 1

    - name: All tests completed
      run: echo "Test suite completed. Check individual job results above."