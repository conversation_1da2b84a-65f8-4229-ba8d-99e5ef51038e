// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in thoughtecho/test/sync_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i8;
import 'dart:ui' as _i14;

import 'package:flutter/material.dart' as _i16;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;
import 'package:sqflite/sqflite.dart' as _i2;
import 'package:thoughtecho/models/ai_analysis_model.dart' as _i6;
import 'package:thoughtecho/models/ai_settings.dart' as _i3;
import 'package:thoughtecho/models/app_settings.dart' as _i4;
import 'package:thoughtecho/models/multi_ai_settings.dart' as _i5;
import 'package:thoughtecho/models/note_category.dart' as _i12;
import 'package:thoughtecho/models/quote_model.dart' as _i13;
import 'package:thoughtecho/services/ai_analysis_database_service.dart' as _i17;
import 'package:thoughtecho/services/backup_service.dart' as _i7;
import 'package:thoughtecho/services/database_service.dart' as _i11;
import 'package:thoughtecho/services/large_file_manager.dart' as _i9;
import 'package:thoughtecho/services/settings_service.dart' as _i15;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDatabase_0 extends _i1.SmartFake implements _i2.Database {
  _FakeDatabase_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAISettings_1 extends _i1.SmartFake implements _i3.AISettings {
  _FakeAISettings_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAppSettings_2 extends _i1.SmartFake implements _i4.AppSettings {
  _FakeAppSettings_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMultiAISettings_3 extends _i1.SmartFake
    implements _i5.MultiAISettings {
  _FakeMultiAISettings_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAIAnalysis_4 extends _i1.SmartFake implements _i6.AIAnalysis {
  _FakeAIAnalysis_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [BackupService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBackupService extends _i1.Mock implements _i7.BackupService {
  MockBackupService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.Future<String> exportAllData({
    required bool? includeMediaFiles,
    String? customPath,
    dynamic Function(
      int,
      int,
    )? onProgress,
    _i9.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #exportAllData,
          [],
          {
            #includeMediaFiles: includeMediaFiles,
            #customPath: customPath,
            #onProgress: onProgress,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i8.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #exportAllData,
            [],
            {
              #includeMediaFiles: includeMediaFiles,
              #customPath: customPath,
              #onProgress: onProgress,
              #cancelToken: cancelToken,
            },
          ),
        )),
      ) as _i8.Future<String>);

  @override
  _i8.Future<void> importData(
    String? filePath, {
    bool? clearExisting = true,
    dynamic Function(
      int,
      int,
    )? onProgress,
    _i9.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #importData,
          [filePath],
          {
            #clearExisting: clearExisting,
            #onProgress: onProgress,
            #cancelToken: cancelToken,
          },
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<bool> validateBackupFile(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #validateBackupFile,
          [filePath],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);
}

/// A class which mocks [DatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockDatabaseService extends _i1.Mock implements _i11.DatabaseService {
  MockDatabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasMoreQuotes => (super.noSuchMethod(
        Invocation.getter(#hasMoreQuotes),
        returnValue: false,
      ) as bool);

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  _i2.Database get database => (super.noSuchMethod(
        Invocation.getter(#database),
        returnValue: _FakeDatabase_0(
          this,
          Invocation.getter(#database),
        ),
      ) as _i2.Database);

  @override
  _i8.Future<_i2.Database> get safeDatabase => (super.noSuchMethod(
        Invocation.getter(#safeDatabase),
        returnValue: _i8.Future<_i2.Database>.value(_FakeDatabase_0(
          this,
          Invocation.getter(#safeDatabase),
        )),
      ) as _i8.Future<_i2.Database>);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i8.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> initializeNewDatabase() => (super.noSuchMethod(
        Invocation.method(
          #initializeNewDatabase,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> initDefaultHitokotoCategories() => (super.noSuchMethod(
        Invocation.method(
          #initDefaultHitokotoCategories,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<Map<String, dynamic>> exportDataAsMap() => (super.noSuchMethod(
        Invocation.method(
          #exportDataAsMap,
          [],
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<String> exportAllData({String? customPath}) => (super.noSuchMethod(
        Invocation.method(
          #exportAllData,
          [],
          {#customPath: customPath},
        ),
        returnValue: _i8.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #exportAllData,
            [],
            {#customPath: customPath},
          ),
        )),
      ) as _i8.Future<String>);

  @override
  _i8.Future<void> importDataFromMap(
    Map<String, dynamic>? data, {
    bool? clearExisting = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #importDataFromMap,
          [data],
          {#clearExisting: clearExisting},
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> importData(
    String? filePath, {
    bool? clearExisting = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #importData,
          [filePath],
          {#clearExisting: clearExisting},
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<bool> checkCanExport() => (super.noSuchMethod(
        Invocation.method(
          #checkCanExport,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> validateBackupFile(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #validateBackupFile,
          [filePath],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<List<_i12.NoteCategory>> getCategories() => (super.noSuchMethod(
        Invocation.method(
          #getCategories,
          [],
        ),
        returnValue:
            _i8.Future<List<_i12.NoteCategory>>.value(<_i12.NoteCategory>[]),
      ) as _i8.Future<List<_i12.NoteCategory>>);

  @override
  _i8.Future<void> addCategory(
    String? name, {
    String? iconName,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCategory,
          [name],
          {#iconName: iconName},
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> addCategoryWithId(
    String? id,
    String? name, {
    String? iconName,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCategoryWithId,
          [
            id,
            name,
          ],
          {#iconName: iconName},
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Stream<List<_i12.NoteCategory>> watchCategories() => (super.noSuchMethod(
        Invocation.method(
          #watchCategories,
          [],
        ),
        returnValue: _i8.Stream<List<_i12.NoteCategory>>.empty(),
      ) as _i8.Stream<List<_i12.NoteCategory>>);

  @override
  _i8.Future<void> deleteCategory(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteCategory,
          [id],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> addQuote(_i13.Quote? quote) => (super.noSuchMethod(
        Invocation.method(
          #addQuote,
          [quote],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<List<_i13.Quote>> getUserQuotes({
    List<String>? tagIds,
    String? categoryId,
    int? offset = 0,
    int? limit = 10,
    String? orderBy = 'date DESC',
    String? searchQuery,
    List<String>? selectedWeathers,
    List<String>? selectedDayPeriods,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserQuotes,
          [],
          {
            #tagIds: tagIds,
            #categoryId: categoryId,
            #offset: offset,
            #limit: limit,
            #orderBy: orderBy,
            #searchQuery: searchQuery,
            #selectedWeathers: selectedWeathers,
            #selectedDayPeriods: selectedDayPeriods,
          },
        ),
        returnValue: _i8.Future<List<_i13.Quote>>.value(<_i13.Quote>[]),
      ) as _i8.Future<List<_i13.Quote>>);

  @override
  Map<String, dynamic> getQueryPerformanceReport() => (super.noSuchMethod(
        Invocation.method(
          #getQueryPerformanceReport,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i8.Future<Map<String, dynamic>> checkTagDataConsistency() =>
      (super.noSuchMethod(
        Invocation.method(
          #checkTagDataConsistency,
          [],
        ),
        returnValue:
            _i8.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i8.Future<Map<String, dynamic>>);

  @override
  _i8.Future<bool> cleanupTagDataInconsistencies() => (super.noSuchMethod(
        Invocation.method(
          #cleanupTagDataInconsistencies,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<List<_i13.Quote>> getAllQuotes() => (super.noSuchMethod(
        Invocation.method(
          #getAllQuotes,
          [],
        ),
        returnValue: _i8.Future<List<_i13.Quote>>.value(<_i13.Quote>[]),
      ) as _i8.Future<List<_i13.Quote>>);

  @override
  _i8.Future<int> getQuotesCount({
    List<String>? tagIds,
    String? categoryId,
    String? searchQuery,
    List<String>? selectedWeathers,
    List<String>? selectedDayPeriods,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getQuotesCount,
          [],
          {
            #tagIds: tagIds,
            #categoryId: categoryId,
            #searchQuery: searchQuery,
            #selectedWeathers: selectedWeathers,
            #selectedDayPeriods: selectedDayPeriods,
          },
        ),
        returnValue: _i8.Future<int>.value(0),
      ) as _i8.Future<int>);

  @override
  _i8.Future<void> deleteQuote(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteQuote,
          [id],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> updateQuote(_i13.Quote? quote) => (super.noSuchMethod(
        Invocation.method(
          #updateQuote,
          [quote],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Stream<List<_i13.Quote>> watchQuotes({
    List<String>? tagIds,
    String? categoryId,
    int? limit = 20,
    String? orderBy = 'date DESC',
    String? searchQuery,
    List<String>? selectedWeathers,
    List<String>? selectedDayPeriods,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchQuotes,
          [],
          {
            #tagIds: tagIds,
            #categoryId: categoryId,
            #limit: limit,
            #orderBy: orderBy,
            #searchQuery: searchQuery,
            #selectedWeathers: selectedWeathers,
            #selectedDayPeriods: selectedDayPeriods,
          },
        ),
        returnValue: _i8.Stream<List<_i13.Quote>>.empty(),
      ) as _i8.Stream<List<_i13.Quote>>);

  @override
  _i8.Future<void> loadMoreQuotes({
    List<String>? tagIds,
    String? categoryId,
    String? searchQuery,
    List<String>? selectedWeathers,
    List<String>? selectedDayPeriods,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #loadMoreQuotes,
          [],
          {
            #tagIds: tagIds,
            #categoryId: categoryId,
            #searchQuery: searchQuery,
            #selectedWeathers: selectedWeathers,
            #selectedDayPeriods: selectedDayPeriods,
          },
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> updateCategory(
    String? id,
    String? name, {
    String? iconName,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCategory,
          [
            id,
            name,
          ],
          {#iconName: iconName},
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> patchQuotesDayPeriod() => (super.noSuchMethod(
        Invocation.method(
          #patchQuotesDayPeriod,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> migrateDayPeriodToKey() => (super.noSuchMethod(
        Invocation.method(
          #migrateDayPeriodToKey,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> migrateWeatherToKey() => (super.noSuchMethod(
        Invocation.method(
          #migrateWeatherToKey,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<_i12.NoteCategory?> getCategoryById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCategoryById,
          [id],
        ),
        returnValue: _i8.Future<_i12.NoteCategory?>.value(),
      ) as _i8.Future<_i12.NoteCategory?>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i14.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [SettingsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSettingsService extends _i1.Mock implements _i15.SettingsService {
  MockSettingsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.AISettings get aiSettings => (super.noSuchMethod(
        Invocation.getter(#aiSettings),
        returnValue: _FakeAISettings_1(
          this,
          Invocation.getter(#aiSettings),
        ),
      ) as _i3.AISettings);

  @override
  _i4.AppSettings get appSettings => (super.noSuchMethod(
        Invocation.getter(#appSettings),
        returnValue: _FakeAppSettings_2(
          this,
          Invocation.getter(#appSettings),
        ),
      ) as _i4.AppSettings);

  @override
  _i16.ThemeMode get themeMode => (super.noSuchMethod(
        Invocation.getter(#themeMode),
        returnValue: _i16.ThemeMode.system,
      ) as _i16.ThemeMode);

  @override
  _i5.MultiAISettings get multiAISettings => (super.noSuchMethod(
        Invocation.getter(#multiAISettings),
        returnValue: _FakeMultiAISettings_3(
          this,
          Invocation.getter(#multiAISettings),
        ),
      ) as _i5.MultiAISettings);

  @override
  bool get aiCardGenerationEnabled => (super.noSuchMethod(
        Invocation.getter(#aiCardGenerationEnabled),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i8.Future<void> updateAISettings(_i3.AISettings? settings) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateAISettings,
          [settings],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> updateAppSettings(_i4.AppSettings? settings) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateAppSettings,
          [settings],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> updateHitokotoType(String? type) => (super.noSuchMethod(
        Invocation.method(
          #updateHitokotoType,
          [type],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> updateThemeMode(_i16.ThemeMode? mode) => (super.noSuchMethod(
        Invocation.method(
          #updateThemeMode,
          [mode],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setAppUpgraded() => (super.noSuchMethod(
        Invocation.method(
          #setAppUpgraded,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setInitialDatabaseSetupComplete(bool? isComplete) =>
      (super.noSuchMethod(
        Invocation.method(
          #setInitialDatabaseSetupComplete,
          [isComplete],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  bool isInitialDatabaseSetupComplete() => (super.noSuchMethod(
        Invocation.method(
          #isInitialDatabaseSetupComplete,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i8.Future<void> setDatabaseMigrationComplete(bool? isComplete) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDatabaseMigrationComplete,
          [isComplete],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  bool isDatabaseMigrationComplete() => (super.noSuchMethod(
        Invocation.method(
          #isDatabaseMigrationComplete,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool hasCompletedOnboarding() => (super.noSuchMethod(
        Invocation.method(
          #hasCompletedOnboarding,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i8.Future<void> setHasCompletedOnboarding(bool? completed) =>
      (super.noSuchMethod(
        Invocation.method(
          #setHasCompletedOnboarding,
          [completed],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setAICardGenerationEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setAICardGenerationEnabled,
          [enabled],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> setAppVersion(String? version) => (super.noSuchMethod(
        Invocation.method(
          #setAppVersion,
          [version],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> saveMultiAISettings(_i5.MultiAISettings? settings) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveMultiAISettings,
          [settings],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> updateMultiAISettings(_i5.MultiAISettings? settings) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMultiAISettings,
          [settings],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  Map<String, dynamic> getAllSettingsForBackup() => (super.noSuchMethod(
        Invocation.method(
          #getAllSettingsForBackup,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i8.Future<void> restoreAllSettingsFromBackup(
          Map<String, dynamic>? backupData) =>
      (super.noSuchMethod(
        Invocation.method(
          #restoreAllSettingsFromBackup,
          [backupData],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  void addListener(_i14.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [AIAnalysisDatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAIAnalysisDatabaseService extends _i1.Mock
    implements _i17.AIAnalysisDatabaseService {
  MockAIAnalysisDatabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.Future<_i2.Database> get database => (super.noSuchMethod(
        Invocation.getter(#database),
        returnValue: _i8.Future<_i2.Database>.value(_FakeDatabase_0(
          this,
          Invocation.getter(#database),
        )),
      ) as _i8.Future<_i2.Database>);

  @override
  _i8.Stream<List<_i6.AIAnalysis>> get analysesStream => (super.noSuchMethod(
        Invocation.getter(#analysesStream),
        returnValue: _i8.Stream<List<_i6.AIAnalysis>>.empty(),
      ) as _i8.Stream<List<_i6.AIAnalysis>>);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i8.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<void> closeDatabase() => (super.noSuchMethod(
        Invocation.method(
          #closeDatabase,
          [],
        ),
        returnValue: _i8.Future<void>.value(),
        returnValueForMissingStub: _i8.Future<void>.value(),
      ) as _i8.Future<void>);

  @override
  _i8.Future<_i6.AIAnalysis> saveAnalysis(_i6.AIAnalysis? analysis) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveAnalysis,
          [analysis],
        ),
        returnValue: _i8.Future<_i6.AIAnalysis>.value(_FakeAIAnalysis_4(
          this,
          Invocation.method(
            #saveAnalysis,
            [analysis],
          ),
        )),
      ) as _i8.Future<_i6.AIAnalysis>);

  @override
  _i8.Future<List<_i6.AIAnalysis>> getAllAnalyses() => (super.noSuchMethod(
        Invocation.method(
          #getAllAnalyses,
          [],
        ),
        returnValue: _i8.Future<List<_i6.AIAnalysis>>.value(<_i6.AIAnalysis>[]),
      ) as _i8.Future<List<_i6.AIAnalysis>>);

  @override
  _i8.Future<_i6.AIAnalysis?> getAnalysisById(String? id) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAnalysisById,
          [id],
        ),
        returnValue: _i8.Future<_i6.AIAnalysis?>.value(),
      ) as _i8.Future<_i6.AIAnalysis?>);

  @override
  _i8.Future<bool> deleteAnalysis(String? id) => (super.noSuchMethod(
        Invocation.method(
          #deleteAnalysis,
          [id],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<bool> deleteAllAnalyses() => (super.noSuchMethod(
        Invocation.method(
          #deleteAllAnalyses,
          [],
        ),
        returnValue: _i8.Future<bool>.value(false),
      ) as _i8.Future<bool>);

  @override
  _i8.Future<List<_i6.AIAnalysis>> searchAnalysesByType(String? analysisType) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchAnalysesByType,
          [analysisType],
        ),
        returnValue: _i8.Future<List<_i6.AIAnalysis>>.value(<_i6.AIAnalysis>[]),
      ) as _i8.Future<List<_i6.AIAnalysis>>);

  @override
  _i8.Future<List<_i6.AIAnalysis>> searchAnalyses(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchAnalyses,
          [query],
        ),
        returnValue: _i8.Future<List<_i6.AIAnalysis>>.value(<_i6.AIAnalysis>[]),
      ) as _i8.Future<List<_i6.AIAnalysis>>);

  @override
  _i8.Future<int> restoreFromJson(String? jsonStr) => (super.noSuchMethod(
        Invocation.method(
          #restoreFromJson,
          [jsonStr],
        ),
        returnValue: _i8.Future<int>.value(0),
      ) as _i8.Future<int>);

  @override
  _i8.Future<String> exportToJson() => (super.noSuchMethod(
        Invocation.method(
          #exportToJson,
          [],
        ),
        returnValue: _i8.Future<String>.value(_i10.dummyValue<String>(
          this,
          Invocation.method(
            #exportToJson,
            [],
          ),
        )),
      ) as _i8.Future<String>);

  @override
  _i8.Future<List<Map<String, dynamic>>> exportAnalysesAsList() =>
      (super.noSuchMethod(
        Invocation.method(
          #exportAnalysesAsList,
          [],
        ),
        returnValue: _i8.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i8.Future<List<Map<String, dynamic>>>);

  @override
  _i8.Future<int> importAnalysesFromList(
          List<Map<String, dynamic>>? analyses) =>
      (super.noSuchMethod(
        Invocation.method(
          #importAnalysesFromList,
          [analyses],
        ),
        returnValue: _i8.Future<int>.value(0),
      ) as _i8.Future<int>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i14.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i14.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
