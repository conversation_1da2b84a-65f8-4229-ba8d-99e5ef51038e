// Mocks generated by Mockito 5.4.6 from annotations
// in thoughtecho/test/widget/note_sync_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:ui' as _i6;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i3;
import 'package:thoughtecho/services/localsend/models/device.dart' as _i5;
import 'package:thoughtecho/services/note_sync_service.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [NoteSyncService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNoteSyncService extends _i1.Mock implements _i2.NoteSyncService {
  MockNoteSyncService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.SyncStatus get syncStatus => (super.noSuchMethod(
        Invocation.getter(#syncStatus),
        returnValue: _i2.SyncStatus.idle,
      ) as _i2.SyncStatus);

  @override
  String get syncStatusMessage => (super.noSuchMethod(
        Invocation.getter(#syncStatusMessage),
        returnValue: _i3.dummyValue<String>(
          this,
          Invocation.getter(#syncStatusMessage),
        ),
      ) as String);

  @override
  double get syncProgress => (super.noSuchMethod(
        Invocation.getter(#syncProgress),
        returnValue: 0.0,
      ) as double);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> startServer() => (super.noSuchMethod(
        Invocation.method(
          #startServer,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> stopServer() => (super.noSuchMethod(
        Invocation.method(
          #stopServer,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendNotesToDevice(_i5.Device? targetDevice) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendNotesToDevice,
          [targetDevice],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> receiveAndMergeNotes(String? backupFilePath) =>
      (super.noSuchMethod(
        Invocation.method(
          #receiveAndMergeNotes,
          [backupFilePath],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String> createSyncPackage(_i5.Device? targetDevice) =>
      (super.noSuchMethod(
        Invocation.method(
          #createSyncPackage,
          [targetDevice],
        ),
        returnValue: _i4.Future<String>.value(_i3.dummyValue<String>(
          this,
          Invocation.method(
            #createSyncPackage,
            [targetDevice],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<void> processSyncPackage(String? backupFilePath) =>
      (super.noSuchMethod(
        Invocation.method(
          #processSyncPackage,
          [backupFilePath],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i5.Device>> discoverNearbyDevices() => (super.noSuchMethod(
        Invocation.method(
          #discoverNearbyDevices,
          [],
        ),
        returnValue: _i4.Future<List<_i5.Device>>.value(<_i5.Device>[]),
      ) as _i4.Future<List<_i5.Device>>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i6.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i6.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
